from flask import Blueprint, request, render_template
from services.modal_service import ModalService
from utils.result import success, error, bad_request
from decorators import login_required

modal_bp = Blueprint('modal', __name__, url_prefix='/modal')
modal_service = ModalService()

class ModalController:
    """模态数据控制器"""
    
    @staticmethod
    @modal_bp.route('/search')
    @login_required
    def search_page():
        """搜索页面"""
        # 检查是否为AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return render_template('modal/modal_search.html', ajax_mode=True)
        return render_template('modal/modal_search.html')

    @staticmethod
    @modal_bp.route('/api/search')
    @login_required
    def search_modal_data():
        """搜索模态数据API"""
        params = {
            'vehicle_model_id': request.args.get('vehicle_model_id'),
            'component_ids': request.args.getlist('component_ids')
        }

        # 参数验证
        if not params.get('vehicle_model_id'):
            return bad_request("请选择车型")

        try:
            data = modal_service.search_modal_data(params)
            return success(data, "查询成功")
        except Exception as e:
            return error(f"查询失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/data/<int:data_id>')
    @login_required
    def get_modal_detail(data_id):
        """获取模态数据详情"""
        try:
            data = modal_service.get_modal_detail(data_id)
            if not data:
                return error("数据不存在", 404)
            return success(data)
        except Exception as e:
            return error(f"获取详情失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/vehicles')
    @login_required
    def get_vehicles():
        """获取车型列表"""
        try:
            vehicles = modal_service.get_vehicle_list()
            return success(vehicles)
        except Exception as e:
            return error(f"获取车型列表失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/components')
    @login_required
    def get_components():
        """获取零部件列表"""
        try:
            vehicle_id = request.args.get('vehicle_id')
            components = modal_service.get_component_list(vehicle_id)
            return success(components)
        except Exception as e:
            return error(f"获取零部件列表失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/test-conditions')
    @login_required
    def get_test_conditions():
        """获取测试状态列表"""
        try:
            conditions = modal_service.get_test_conditions_list()
            return success(conditions)
        except Exception as e:
            return error(f"获取测试状态列表失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/mode-shapes')
    @login_required
    def get_mode_shapes():
        """获取模态振型列表"""
        try:
            mode_shapes = modal_service.get_mode_shapes_list()
            return success(mode_shapes)
        except Exception as e:
            return error(f"获取模态振型列表失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/component-options/<int:component_id>')
    @login_required
    def get_component_options(component_id):
        """根据零件ID获取对应的测试状态和模态振型选项"""
        try:
            options = modal_service.get_component_options(component_id)
            return success(options)
        except Exception as e:
            return error(f"获取零件选项失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/comparison')
    @login_required
    def get_modal_comparison():
        """获取模态对标数据"""
        try:
            params = {
                'component_ids': request.args.getlist('component_ids'),
                'vehicle_ids': request.args.getlist('vehicle_ids'),
                'test_conditions': request.args.getlist('test_conditions'),
                'mode_shapes': request.args.getlist('mode_shapes')
            }

            # 参数验证
            if not params['component_ids']:
                return bad_request("请选择零件")
            if not params['vehicle_ids']:
                return bad_request("请选择车型")
            if not params['test_conditions']:
                return bad_request("请选择测试状态")
            if not params['mode_shapes']:
                return bad_request("请选择模态振型")

            data = modal_service.get_modal_comparison_data(params)
            return success(data, "对标数据获取成功")
        except Exception as e:
            return error(f"获取对标数据失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/find-modal-data')
    @login_required
    def find_modal_data():
        """根据条件查找模态数据"""
        try:
            params = request.args.to_dict()

            # 参数验证
            required_params = ['vehicle_condition', 'mode_shape', 'frequency']
            for param in required_params:
                if not params.get(param):
                    return bad_request(f"缺少必要参数: {param}")

            data = modal_service.find_modal_data_by_conditions(params)
            if data:
                return success(data, "查找成功")
            else:
                return error("未找到匹配的模态数据", 404)
        except Exception as e:
            return error(f"查找模态数据失败: {str(e)}")
