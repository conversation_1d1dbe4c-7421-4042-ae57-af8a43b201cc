import csv
import io
from flask import Blueprint, request, render_template, make_response, send_file
from services.sound_insulation_service import SoundInsulationService
from utils.result import success, error, bad_request
from decorators import login_required

sound_insulation_bp = Blueprint('sound_insulation', __name__, url_prefix='/sound_insulation')
sound_insulation_service = SoundInsulationService()

class SoundInsulationController:
    """吸隔声控制器"""
    
    @staticmethod
    @sound_insulation_bp.route('/area_comparison')
    @login_required
    def area_comparison_page():
        """区域隔声量对比页面"""
        # 检查是否为AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return render_template('sound_module/area_comparison.html', ajax_mode=True)
        return render_template('sound_module/area_comparison.html')
    
    @staticmethod
    @sound_insulation_bp.route('/api/areas')
    @login_required
    def get_areas():
        """获取测试区域列表"""
        try:
            areas = sound_insulation_service.get_area_list()
            return success(areas)
        except Exception as e:
            return error(f"获取区域列表失败: {str(e)}")
    
    @staticmethod
    @sound_insulation_bp.route('/api/vehicles')
    @login_required
    def get_vehicles():
        """获取车型列表"""
        try:
            area_id = request.args.get('area_id', type=int)
            vehicles = sound_insulation_service.get_vehicle_list(area_id)
            return success(vehicles)
        except Exception as e:
            return error(f"获取车型列表失败: {str(e)}")
    
    @staticmethod
    @sound_insulation_bp.route('/api/comparison', methods=['POST'])
    @login_required
    def generate_comparison():
        """生成区域隔声量对比数据"""
        try:
            data = request.get_json()
            area_id = data.get('area_id')
            vehicle_ids = data.get('vehicle_ids', [])
            
            if not area_id:
                return bad_request("请选择测试区域")
            
            if not vehicle_ids:
                return bad_request("请选择至少一个车型")
            
            # 转换为整数列表
            area_id = int(area_id)
            vehicle_ids = [int(vid) for vid in vehicle_ids]
            
            comparison_data = sound_insulation_service.generate_comparison_data(area_id, vehicle_ids)
            return success(comparison_data, "对比数据生成成功")
        except ValueError as e:
            return bad_request(str(e))
        except Exception as e:
            return error(f"生成对比数据失败: {str(e)}")
    
    @staticmethod
    @sound_insulation_bp.route('/api/test_image')
    @login_required
    def get_test_image():
        """获取测试图片信息"""
        try:
            vehicle_id = request.args.get('vehicle_id', type=int)
            area_id = request.args.get('area_id', type=int)
            
            if not vehicle_id or not area_id:
                return bad_request("缺少必要参数")
            
            image_info = sound_insulation_service.get_test_image(vehicle_id, area_id)
            if not image_info:
                return error("未找到测试图片信息")
            
            return success(image_info)
        except Exception as e:
            return error(f"获取测试图片失败: {str(e)}")
    
    @staticmethod
    @sound_insulation_bp.route('/api/export', methods=['POST'])
    @login_required
    def export_data():
        """导出对比数据"""
        try:
            data = request.get_json()
            area_id = data.get('area_id')
            vehicle_ids = data.get('vehicle_ids', [])
            
            if not area_id or not vehicle_ids:
                return bad_request("缺少必要参数")
            
            # 转换为整数列表
            area_id = int(area_id)
            vehicle_ids = [int(vid) for vid in vehicle_ids]
            
            csv_data = sound_insulation_service.export_comparison_data(area_id, vehicle_ids)
            
            # 创建CSV文件
            output = io.StringIO()
            writer = csv.writer(output)
            for row in csv_data:
                writer.writerow(row)
            
            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = 'attachment; filename=sound_insulation_comparison.csv'
            
            return response
            
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")

    # ========== 车型隔声量对比功能 ==========

    @staticmethod
    @sound_insulation_bp.route('/vehicle_insulation')
    @login_required
    def vehicle_insulation_page():
        """车型隔声量对比页面"""
        return render_template('sound_module/vehicle_insulation.html')

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_insulation/vehicles')
    @login_required
    def get_vehicle_insulation_vehicles():
        """获取有隔声量数据的车型列表"""
        try:
            vehicles = sound_insulation_service.get_vehicle_insulation_list()
            return success(vehicles)
        except Exception as e:
            return error(f"获取车型列表失败: {str(e)}")

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_insulation/comparison', methods=['POST'])
    @login_required
    def generate_vehicle_insulation_comparison():
        """生成车型隔声量对比数据"""
        try:
            data = request.get_json()
            vehicle_ids = data.get('vehicle_ids', [])

            if not vehicle_ids:
                return bad_request("请选择至少一个车型")

            # 转换为整数列表
            vehicle_ids = [int(vid) for vid in vehicle_ids]

            comparison_data = sound_insulation_service.generate_vehicle_insulation_comparison(vehicle_ids)
            return success(comparison_data, "车型隔声量对比数据生成成功")
        except ValueError as e:
            return bad_request(str(e))
        except Exception as e:
            return error(f"生成车型隔声量对比数据失败: {str(e)}")

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_insulation/test_image')
    @login_required
    def get_vehicle_insulation_test_image():
        """获取车型隔声量测试图片信息"""
        try:
            vehicle_id = request.args.get('vehicle_id', type=int)

            if not vehicle_id:
                return bad_request("缺少车型ID参数")

            image_info = sound_insulation_service.get_vehicle_insulation_test_image(vehicle_id)
            if not image_info:
                return error("未找到测试图片信息")

            return success(image_info)
        except Exception as e:
            return error(f"获取车型隔声量测试图片失败: {str(e)}")

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_insulation/export', methods=['POST'])
    @login_required
    def export_vehicle_insulation_data():
        """导出车型隔声量对比数据"""
        try:
            data = request.get_json()
            vehicle_ids = data.get('vehicle_ids', [])

            if not vehicle_ids:
                return bad_request("缺少车型ID参数")

            # 转换为整数列表
            vehicle_ids = [int(vid) for vid in vehicle_ids]

            csv_data = sound_insulation_service.export_vehicle_insulation_data(vehicle_ids)

            # 创建CSV文件
            output = io.StringIO()
            writer = csv.writer(output)
            for row in csv_data:
                writer.writerow(row)

            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = 'attachment; filename=vehicle_insulation_comparison.csv'

            return response

        except Exception as e:
            return error(f"导出车型隔声量数据失败: {str(e)}")

    # ========== 车型混响时间对比功能 ==========

    @staticmethod
    @sound_insulation_bp.route('/vehicle_reverberation')
    @login_required
    def vehicle_reverberation_page():
        """车型混响时间对比页面"""
        return render_template('sound_module/vehicle_reverberation.html')

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_reverberation/vehicles')
    @login_required
    def get_vehicle_reverberation_vehicles():
        """获取有混响时间数据的车型列表"""
        try:
            vehicles = sound_insulation_service.get_vehicle_reverberation_list()
            return success(vehicles)
        except Exception as e:
            return error(f"获取车型列表失败: {str(e)}")

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_reverberation/comparison', methods=['POST'])
    @login_required
    def generate_vehicle_reverberation_comparison():
        """生成车型混响时间对比数据"""
        try:
            data = request.get_json()
            vehicle_ids = data.get('vehicle_ids', [])

            if not vehicle_ids:
                return bad_request("请选择至少一个车型")

            # 转换为整数列表
            vehicle_ids = [int(vid) for vid in vehicle_ids]

            comparison_data = sound_insulation_service.generate_vehicle_reverberation_comparison(vehicle_ids)
            return success(comparison_data, "车型混响时间对比数据生成成功")
        except ValueError as e:
            return bad_request(str(e))
        except Exception as e:
            return error(f"生成车型混响时间对比数据失败: {str(e)}")

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_reverberation/test_image')
    @login_required
    def get_vehicle_reverberation_test_image():
        """获取车型混响时间测试图片信息"""
        try:
            vehicle_id = request.args.get('vehicle_id', type=int)

            if not vehicle_id:
                return bad_request("缺少车型ID参数")

            image_info = sound_insulation_service.get_vehicle_reverberation_test_image(vehicle_id)
            if not image_info:
                return error("未找到测试图片信息")

            return success(image_info)
        except Exception as e:
            return error(f"获取车型混响时间测试图片失败: {str(e)}")

    @staticmethod
    @sound_insulation_bp.route('/api/vehicle_reverberation/export', methods=['POST'])
    @login_required
    def export_vehicle_reverberation_data():
        """导出车型混响时间对比数据"""
        try:
            data = request.get_json()
            vehicle_ids = data.get('vehicle_ids', [])

            if not vehicle_ids:
                return bad_request("缺少车型ID参数")

            # 转换为整数列表
            vehicle_ids = [int(vid) for vid in vehicle_ids]

            csv_data = sound_insulation_service.export_vehicle_reverberation_data(vehicle_ids)

            # 创建CSV文件
            output = io.StringIO()
            writer = csv.writer(output)
            for row in csv_data:
                writer.writerow(row)

            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = 'attachment; filename=vehicle_reverberation_comparison.csv'

            return response

        except Exception as e:
            return error(f"导出车型混响时间数据失败: {str(e)}")
