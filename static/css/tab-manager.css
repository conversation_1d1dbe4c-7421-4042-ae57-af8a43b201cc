/**
 * 标签页管理器样式
 */

/* 标签页导航栏 */
.tab-navigation {
    margin-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    padding: 0.5rem 1rem 0;
    border-radius: 0.375rem 0.375rem 0 0;
}

/* 标签页导航 */
#mainTabs {
    border-bottom: none;
    margin-bottom: 0;
}

#mainTabs .nav-item {
    margin-bottom: 0;
}

#mainTabs .nav-link {
    border: 1px solid transparent;
    border-bottom: none;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.5rem 0.75rem;
    margin-right: 0.25rem;
    border-radius: 0.375rem 0.375rem 0 0;
    position: relative;
    display: flex;
    align-items: center;
    max-width: 200px;
    transition: all 0.15s ease-in-out;
}

#mainTabs .nav-link:hover {
    background-color: #dee2e6;
    color: #212529;
    border-color: #dee2e6;
}

#mainTabs .nav-link.active {
    background-color: #fff;
    color: #495057;
    border-color: #dee2e6 #dee2e6 #fff;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
}

/* 标签页标题 */
.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 0.25rem;
}

/* 关闭按钮 */
#mainTabs .btn-close {
    width: 0.75rem;
    height: 0.75rem;
    padding: 0;
    margin: 0;
    background-size: 0.5rem;
    opacity: 0.5;
    flex-shrink: 0;
}

#mainTabs .btn-close:hover {
    opacity: 0.75;
}

#mainTabs .nav-link.active .btn-close {
    opacity: 0.7;
}

#mainTabs .nav-link.active .btn-close:hover {
    opacity: 1;
}

/* 标签页内容 */
#tabContent {
    background-color: #fff;
    border-radius: 0 0 0.375rem 0.375rem;
    min-height: 400px;
}

#tabContent .tab-pane {
    padding: 0;
}

/* 当没有标签页时，首页内容的样式 */
#home-tab-pane {
    padding: 1rem;
}

/* 加载状态 */
.tab-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #6c757d;
}

/* 错误状态 */
.tab-error {
    padding: 2rem;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tab-navigation {
        padding: 0.25rem 0.5rem 0;
    }
    
    #mainTabs .nav-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
        max-width: 150px;
    }
    
    .tab-title {
        font-size: 0.8rem;
    }
    
    #mainTabs .btn-close {
        width: 0.6rem;
        height: 0.6rem;
        background-size: 0.4rem;
    }
}

/* 标签页动画 */
.tab-pane {
    transition: opacity 0.15s ease-in-out;
}

.tab-pane.fade {
    opacity: 0;
}

.tab-pane.fade.show {
    opacity: 1;
}

/* 标签页图标 */
#mainTabs .nav-link i {
    font-size: 0.875rem;
    flex-shrink: 0;
}

/* 标签页悬浮效果 */
#mainTabs .nav-item {
    position: relative;
}

#mainTabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: transparent;
    transition: background-color 0.15s ease-in-out;
}

#mainTabs .nav-link.active::before {
    background-color: #007bff;
}

/* 标签页拖拽效果（预留） */
#mainTabs .nav-link.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* 标签页关闭确认 */
.tab-close-confirm {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    z-index: 1000;
    white-space: nowrap;
}

/* 标签页数量限制提示 */
.tab-limit-warning {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 300px;
}

/* 标签页内容区域的特殊样式 */
.tab-content-wrapper {
    position: relative;
    min-height: 500px;
}

/* 标签页切换时的加载遮罩 */
.tab-switching-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

/* 标签页标题编辑 */
.tab-title-edit {
    background: transparent;
    border: none;
    outline: none;
    font-size: inherit;
    color: inherit;
    width: 100%;
}

/* 标签页右键菜单 */
.tab-context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1050;
    min-width: 150px;
}

.tab-context-menu .dropdown-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.tab-context-menu .dropdown-item:hover {
    background-color: #f8f9fa;
}

.tab-context-menu .dropdown-divider {
    margin: 0.25rem 0;
}

/* 标签页状态指示器 */
.tab-status-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #28a745;
}

.tab-status-indicator.loading {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

.tab-status-indicator.error {
    background-color: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
