/**
 * 材料孔隙率流阻查询功能
 */

// 材料多选下拉框组件
class MaterialMultiSelect {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.selectedMaterials = [];
        this.allMaterials = [];
        this.isOpen = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        const inputContainer = this.container.querySelector('.multiselect-input-container');
        const searchInput = this.container.querySelector('.multiselect-search input');

        // 点击输入框切换下拉菜单
        inputContainer.addEventListener('click', () => {
            this.toggle();
        });

        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            this.filterOptions(e.target.value);
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        });
    }

    loadMaterials(materials) {
        this.allMaterials = materials;
        this.renderOptions();
        this.updatePlaceholder();
    }

    renderOptions() {
        const optionsContainer = this.container.querySelector('.multiselect-options');
        optionsContainer.innerHTML = '';

        if (this.allMaterials.length === 0) {
            optionsContainer.innerHTML = '<div class="text-muted text-center py-2">暂无材料数据</div>';
            return;
        }

        this.allMaterials.forEach(material => {
            const option = document.createElement('div');
            option.className = 'multiselect-option';
            option.innerHTML = `
                <input type="checkbox" id="material-${material.name}" ${this.isSelected(material.name) ? 'checked' : ''}>
                <label for="material-${material.name}">${material.name}</label>
            `;

            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const checkbox = option.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
                this.toggleMaterial(material);
            });

            optionsContainer.appendChild(option);
        });
    }

    filterOptions(searchTerm) {
        const options = this.container.querySelectorAll('.multiselect-option');
        options.forEach(option => {
            const label = option.querySelector('label').textContent.toLowerCase();
            const isVisible = label.includes(searchTerm.toLowerCase());
            option.style.display = isVisible ? 'flex' : 'none';
        });
    }

    toggleMaterial(material) {
        const index = this.selectedMaterials.findIndex(m => m.name === material.name);
        if (index > -1) {
            this.selectedMaterials.splice(index, 1);
        } else {
            this.selectedMaterials.push(material);
        }

        this.updateDisplay();
        this.updateSelectedOptions();
        this.onSelectionChange();
    }

    isSelected(materialName) {
        return this.selectedMaterials.some(m => m.name === materialName);
    }

    updateDisplay() {
        const input = this.container.querySelector('.multiselect-input');
        const selectedItemsContainer = this.container.querySelector('.selected-items');

        if (this.selectedMaterials.length === 0) {
            input.placeholder = this.allMaterials.length > 0 ? '请选择材料...' : '请先选择零件...';
            selectedItemsContainer.innerHTML = '';
        } else {
            input.placeholder = `已选择 ${this.selectedMaterials.length} 个材料`;
            this.renderSelectedItems();
        }
    }

    renderSelectedItems() {
        const selectedItemsContainer = this.container.querySelector('.selected-items');
        selectedItemsContainer.innerHTML = '';

        this.selectedMaterials.forEach(material => {
            const item = document.createElement('div');
            item.className = 'selected-item';
            item.innerHTML = `
                <span>${material.name}</span>
                <button type="button" class="remove-btn" data-material="${material.name}">×</button>
            `;

            const removeBtn = item.querySelector('.remove-btn');
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeMaterial(material.name);
            });

            selectedItemsContainer.appendChild(item);
        });
    }

    removeMaterial(materialName) {
        const index = this.selectedMaterials.findIndex(m => m.name === materialName);
        if (index > -1) {
            this.selectedMaterials.splice(index, 1);
            this.updateDisplay();
            this.updateSelectedOptions();
            this.onSelectionChange();
        }
    }

    updateSelectedOptions() {
        const options = this.container.querySelectorAll('.multiselect-option');
        options.forEach(option => {
            const checkbox = option.querySelector('input[type="checkbox"]');
            const materialName = checkbox.id.replace('material-', '');
            const isSelected = this.isSelected(materialName);

            checkbox.checked = isSelected;
            option.classList.toggle('selected', isSelected);
        });
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        if (this.allMaterials.length === 0) return;

        this.container.querySelector('.multiselect-container').classList.add('open');
        this.isOpen = true;

        // 清空搜索框
        const searchInput = this.container.querySelector('.multiselect-search input');
        searchInput.value = '';
        this.filterOptions('');

        // 聚焦搜索框
        setTimeout(() => searchInput.focus(), 100);
    }

    close() {
        this.container.querySelector('.multiselect-container').classList.remove('open');
        this.isOpen = false;
    }

    updatePlaceholder() {
        const input = this.container.querySelector('.multiselect-input');
        if (this.allMaterials.length === 0) {
            input.placeholder = '请先选择零件...';
        } else if (this.selectedMaterials.length === 0) {
            input.placeholder = '请选择材料...';
        }
    }

    clear() {
        this.selectedMaterials = [];
        this.allMaterials = [];
        this.updateDisplay();
        this.close();
    }

    getSelectedMaterials() {
        return this.selectedMaterials;
    }

    onSelectionChange() {
        // 子类可以重写此方法
    }
}

class MaterialPorosityFlowResistanceQuery {
    constructor() {
        this.currentData = null;
        this.materialMultiSelect = null;
        this.init();
    }

    init() {
        this.initMultiSelect();
        this.bindEvents();
        this.loadParts();
    }

    initMultiSelect() {
        this.materialMultiSelect = new MaterialMultiSelect('material-multiselect');
        this.materialMultiSelect.onSelectionChange = () => this.updateButtonStates();
    }

    bindEvents() {
        // 零件选择变化事件
        $('#part-select').on('change', () => this.onPartChange());

        // 查询按钮点击事件
        $('#query-btn').on('click', () => this.queryData());

        // 重置按钮点击事件
        $('#reset-btn').on('click', () => this.resetForm());

        // 导出按钮点击事件
        $('#export-btn').on('click', () => this.exportData());
    }

    async loadParts() {
        try {
            const response = await fetch('/material_porosity_flow_resistance/api/parts');
            const result = await response.json();
            
            if (result.code === 200) {
                this.populatePartSelect(result.data);
            } else {
                this.showError('加载零件列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载零件列表失败: ' + error.message);
        }
    }

    populatePartSelect(parts) {
        const $select = $('#part-select');
        $select.empty();
        $select.append('<option value="">请选择零件</option>');

        if (parts.length === 0) {
            $select.append('<option value="" disabled>暂无零件数据</option>');
            return;
        }

        parts.forEach(part => {
            $select.append(`<option value="${part.name}">${part.name}</option>`);
        });
    }

    async onPartChange() {
        this.materialMultiSelect.clear(); // 清空已选材料
        this.loadMaterials();
        this.updateButtonStates();
    }

    async loadMaterials() {
        const selectedPart = $('#part-select').val();

        try {
            let url = '/material_porosity_flow_resistance/api/materials';
            if (selectedPart) {
                url += `?part_names=${encodeURIComponent(selectedPart)}`;
            }

            const response = await fetch(url);
            const result = await response.json();

            if (result.code === 200) {
                this.materialMultiSelect.loadMaterials(result.data);
            } else {
                this.showError('加载材料列表失败: ' + result.message);
                this.materialMultiSelect.loadMaterials([]);
            }
        } catch (error) {
            this.showError('加载材料列表失败: ' + error.message);
            this.materialMultiSelect.loadMaterials([]);
        }

        this.updateButtonStates();
    }

    updateButtonStates() {
        const selectedPart = $('#part-select').val();
        const selectedMaterials = this.materialMultiSelect.getSelectedMaterials();
        const hasSelection = selectedPart && selectedMaterials.length > 0;

        $('#query-btn').prop('disabled', !hasSelection);
        $('#export-btn').prop('disabled', !this.currentData || this.currentData.length === 0);
    }

    async queryData() {
        const selectedPart = $('#part-select').val();
        const selectedMaterials = this.materialMultiSelect.getSelectedMaterials();

        if (!selectedPart || selectedMaterials.length === 0) {
            this.showError('请选择零件和至少一个材料');
            return;
        }

        this.showLoading();
        this.hideError();

        try {
            let url = '/material_porosity_flow_resistance/api/query_data?';
            const params = [];

            params.push(`part_names=${encodeURIComponent(selectedPart)}`);

            selectedMaterials.forEach(material => {
                params.push(`material_names=${encodeURIComponent(material.name)}`);
            });

            url += params.join('&');

            const response = await fetch(url);
            const result = await response.json();

            if (result.code === 200) {
                this.currentData = result.data;
                this.displayResults(result.data);
                this.updateButtonStates();
            } else {
                this.showError(result.message);
                this.hideResults();
            }
        } catch (error) {
            this.showError('查询失败: ' + error.message);
            this.hideResults();
        } finally {
            this.hideLoading();
        }
    }

    displayResults(data) {
        this.hideEmptyState();
        this.showResults();

        // 更新记录数量
        $('#result-count').text(`${data.length} 条记录`);

        // 生成表格内容
        const $tbody = $('#data-table tbody');
        $tbody.empty();

        if (data.length === 0) {
            $tbody.append(`
                <tr>
                    <td colspan="9" class="text-center text-muted">暂无数据</td>
                </tr>
            `);
            return;
        }

        data.forEach(item => {
            const row = `
                <tr>
                    <td>${item.part_name || '-'}</td>
                    <td>${item.material_name || '-'}</td>
                    <td>${item.thickness || '-'}</td>
                    <td>${item.weight || '-'}</td>
                    <td>${item.density || '-'}</td>
                    <td>${item.porosity || '-'}</td>
                    <td>${item.porosity_deviation || '-'}</td>
                    <td>${item.flow_resistance || '-'}</td>
                    <td>${item.flow_resistance_deviation || '-'}</td>
                </tr>
            `;
            $tbody.append(row);
        });
    }

    async exportData() {
        const selectedPart = $('#part-select').val();
        const selectedMaterials = this.materialMultiSelect.getSelectedMaterials();

        if (!selectedPart || selectedMaterials.length === 0) {
            this.showError('请选择零件和至少一个材料');
            return;
        }

        try {
            let url = '/material_porosity_flow_resistance/api/export_csv?';
            const params = [];

            params.push(`part_names=${encodeURIComponent(selectedPart)}`);

            selectedMaterials.forEach(material => {
                params.push(`material_names=${encodeURIComponent(material.name)}`);
            });

            url += params.join('&');

            window.open(url, '_blank');
        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    resetForm() {
        $('#part-select').val('');
        this.materialMultiSelect.clear();
        this.currentData = null;
        this.hideResults();
        this.hideError();
        this.showEmptyState();
        this.updateButtonStates();
    }

    showLoading() {
        $('#loading-indicator').show();
    }

    hideLoading() {
        $('#loading-indicator').hide();
    }

    showResults() {
        $('#results-card').show();
    }

    hideResults() {
        $('#results-card').hide();
    }

    showEmptyState() {
        $('#empty-state').show();
    }

    hideEmptyState() {
        $('#empty-state').hide();
    }

    showError(message) {
        $('#error-message').text(message);
        $('#error-alert').show();
    }

    hideError() {
        $('#error-alert').hide();
    }
}
