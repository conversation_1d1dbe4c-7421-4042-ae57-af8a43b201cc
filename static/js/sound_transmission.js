/**
 * 垂直入射法隔声量查询功能
 */

class SoundTransmissionQuery {
    constructor() {
        this.chart = null;
        this.currentData = null;
        this.availableWeights = [];
        this.resizeListenerAdded = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadParts();
    }

    bindEvents() {
        // 零件选择事件
        $('#part-select').on('change', () => {
            this.onPartChange();
        });

        // 材料选择事件
        $('#material-select').on('change', () => {
            this.onMaterialChange();
        });

        // 克重选择事件
        $('#weight-select').on('change', () => {
            this.onWeightChange();
        });

        // 查询按钮
        $('#search-btn').on('click', () => {
            this.searchData();
        });

        // 导出按钮
        $('#export-btn').on('click', () => {
            this.exportData();
        });

        // 查看测试附图按钮
        $('#view-image-btn').on('click', () => {
            this.showTestImage();
        });

        // 窗口大小改变事件
        $(window).on('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });
    }

    async loadParts() {
        try {
            const response = await fetch('/sound_transmission/api/parts');
            const result = await response.json();
            
            if (result.code === 200) {
                this.populatePartSelect(result.data);
            } else {
                this.showError('加载零件列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载零件列表失败: ' + error.message);
        }
    }

    populatePartSelect(parts) {
        const select = $('#part-select');
        select.empty().append('<option value="">请选择零件</option>');
        
        parts.forEach(part => {
            select.append(`<option value="${part.name}" title="${part.description || ''}">${part.name}</option>`);
        });
    }

    async onPartChange() {
        const partName = $('#part-select').val();
        const materialSelect = $('#material-select');
        const weightSelect = $('#weight-select');
        
        // 重置后续选择框
        materialSelect.empty().append('<option value="">请选择材料</option>').prop('disabled', !partName);
        weightSelect.empty().append('<option value="">请先选择材料</option>').prop('disabled', true);
        this.updateButtonStates();
        
        if (!partName) return;
        
        try {
            const response = await fetch(`/sound_transmission/api/materials?part_name=${encodeURIComponent(partName)}`);
            const result = await response.json();
            
            if (result.code === 200) {
                this.populateMaterialSelect(result.data);
            } else {
                this.showError('加载材料列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载材料列表失败: ' + error.message);
        }
    }

    populateMaterialSelect(materials) {
        const select = $('#material-select');
        select.prop('disabled', false);
        
        materials.forEach(material => {
            select.append(`<option value="${material.name}" title="${material.description || ''}">${material.name}</option>`);
        });
    }

    async onMaterialChange() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weightSelect = $('#weight-select');
        
        // 重置克重选择框
        weightSelect.empty().append('<option value="">请选择克重</option>').prop('disabled', !materialName);
        this.updateButtonStates();
        
        if (!partName || !materialName) return;
        
        try {
            const response = await fetch(`/sound_transmission/api/weights?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}`);
            const result = await response.json();
            
            if (result.code === 200) {
                this.populateWeightSelect(result.data);
            } else {
                this.showError('加载克重列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载克重列表失败: ' + error.message);
        }
    }

    populateWeightSelect(weights) {
        const select = $('#weight-select');
        select.prop('disabled', false);
        
        this.availableWeights = weights.map(w => w.weight);
        
        weights.forEach(weight => {
            select.append(`<option value="${weight.weight}">${weight.weight}g/m²</option>`);
        });
    }

    onWeightChange() {
        this.updateButtonStates();
    }

    updateButtonStates() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();
        
        const canSearch = partName && materialName && weight;
        $('#search-btn').prop('disabled', !canSearch);
        $('#export-btn').prop('disabled', !canSearch || !this.currentData);
    }

    async searchData() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();
        
        if (!partName || !materialName || !weight) {
            this.showError('请选择完整的查询条件');
            return;
        }
        
        this.showLoading(true);
        this.hideError();
        
        try {
            const response = await fetch(`/sound_transmission/api/transmission_data?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${encodeURIComponent(weight)}`);
            const result = await response.json();
            
            if (result.code === 200) {
                this.currentData = result.data;
                this.displayData(result.data);
                this.updateButtonStates();
            } else {
                this.showError(result.message);
                this.hideDataDisplay();
            }
        } catch (error) {
            this.showError('查询失败: ' + error.message);
            this.hideDataDisplay();
        } finally {
            this.showLoading(false);
        }
    }

    displayData(data) {
        this.hideError();
        this.displayBasicInfo(data);
        this.displayDataTable(data);
        this.showDataDisplay();

        // 延迟显示图表，确保容器已完全渲染
        setTimeout(() => {
            this.displayChart(data);
        }, 100);
    }

    displayBasicInfo(data) {
        const basicInfo = $('#basic-info');
        basicInfo.empty();
        
        const infoItems = [
            { label: '零件名称', value: data.part_name },
            { label: '材料名称', value: data.material_name },
            { label: '厚度', value: data.thickness ? `${data.thickness}mm` : '-' },
            { label: '克重', value: data.weight ? `${data.weight}g/m²` : '-' },
            { label: '厂家', value: data.manufacturer_name || '-' },
            { label: '测试机构', value: data.test_institution || '-' },
            { label: '测试日期', value: data.test_date || '-' },
            { label: '测试地点', value: data.test_location || '-' },
            { label: '测试工程师', value: data.test_engineer || '-' }
        ];
        
        infoItems.forEach(item => {
            basicInfo.append(`
                <div class="col-md-4 col-lg-3 mb-2">
                    <strong>${item.label}:</strong> ${item.value}
                </div>
            `);
        });

        // 添加备注
        if (data.remarks) {
            basicInfo.append(`
                <div class="col-12 mb-2">
                    <strong>备注:</strong> ${data.remarks}
                </div>
            `);
        }
    }

    displayDataTable(data) {
        const thead = $('#data-table thead');
        const tbody = $('#data-table tbody');

        // 清空现有内容
        tbody.empty();

        // 获取频率标签
        const frequencies = ['125Hz', '160Hz','200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz',
                           '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz',
                           '6300Hz', '8000Hz', '10000Hz'];

        // 生成表头 - 频率作为列标题
        let headerRow = '<tr><th style="width: 60px; min-width: 60px;">频率</th>';
        frequencies.forEach(freq => {
            headerRow += `<th class="text-center" style="width: 80px; min-width: 80px; font-size: 0.85em;">${freq}</th>`;
        });
        headerRow += '</tr>';
        thead.html(headerRow);

        // 生成测试值行
        let testValueRow = '<tr><td><strong>测试</strong></td>';
        frequencies.forEach(freq => {
            const value = data.test_frequency_data[freq];
            const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
            testValueRow += `<td class="text-center">${displayValue}</td>`;
        });
        testValueRow += '</tr>';
        tbody.append(testValueRow);

        // 生成目标值行
        let targetValueRow = '<tr><td><strong>目标</strong></td>';
        frequencies.forEach(freq => {
            const value = data.target_frequency_data[freq];
            const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
            targetValueRow += `<td class="text-center">${displayValue}</td>`;
        });
        targetValueRow += '</tr>';
        tbody.append(targetValueRow);
    }

    displayChart(data) {
        const container = document.getElementById('chart-container');

        if (this.chart) {
            this.chart.dispose();
        }

        if (typeof echarts === 'undefined') {
            console.error('ECharts未加载');
            return;
        }

        // 确保容器可见后再初始化图表
        setTimeout(() => {
            // 检查容器是否可见
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn('Chart container is not visible, retrying...');
                setTimeout(() => this.displayChart(data), 100);
                return;
            }

            this.chart = echarts.init(container);

        // 准备数据
        const frequencies = ['125Hz', '160Hz','200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz',
                           '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz',
                           '6300Hz', '8000Hz', '10000Hz'];

        const testValues = frequencies.map(freq => {
            const value = data.test_frequency_data[freq];
            return value !== null && value !== undefined ? value : null;
        });

        const targetValues = frequencies.map(freq => {
            const value = data.target_frequency_data[freq];
            return value !== null && value !== undefined ? value : null;
        });

        const option = {
            title: {
                text: '隔声量对比曲线',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params) {
                    let result = `<strong>${params[0].axisValue}</strong><br/>`;
                    params.forEach(param => {
                        if (param.value !== null) {
                            result += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)}dB<br/>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: ['测试值', '目标值'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: frequencies,
                name: '频率',
                nameLocation: 'middle',
                nameGap: 30,
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                name: '隔声量(dB)',
                nameLocation: 'middle',
                nameGap: 50,
                axisLabel: {
                    formatter: '{value}dB'
                }
            },
            series: [
                {
                    name: '测试值',
                    type: 'line',
                    data: testValues,
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                        width: 2,
                        color: '#1890ff'
                    },
                    itemStyle: {
                        color: '#1890ff'
                    },
                    connectNulls: false
                },
                {
                    name: '目标值',
                    type: 'line',
                    data: targetValues,
                    symbol: 'diamond',
                    symbolSize: 6,
                    lineStyle: {
                        width: 2,
                        color: '#52c41a',
                        type: 'dashed'
                    },
                    itemStyle: {
                        color: '#52c41a'
                    },
                    connectNulls: false
                }
            ]
        };

            this.chart.setOption(option);

            // 强制触发resize以确保图表正确显示
            setTimeout(() => {
                if (this.chart) {
                    this.chart.resize();
                }
            }, 50);

        }, 100);

        // 响应式调整 - 移除重复的事件监听器
        if (!this.resizeListenerAdded) {
            window.addEventListener('resize', () => {
                if (this.chart) {
                    this.chart.resize();
                }
            });
            this.resizeListenerAdded = true;
        }
    }

    async exportData() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();

        if (!partName || !materialName || !weight) {
            this.showError('请选择完整的查询条件');
            return;
        }

        try {
            const url = `/sound_transmission/api/export_csv?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${encodeURIComponent(weight)}`;
            window.open(url, '_blank');
        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    showDataDisplay() {
        $('#empty-state').hide();
        $('#data-display').show();
    }

    hideDataDisplay() {
        $('#data-display').hide();
        $('#empty-state').show();
    }

    showLoading(show) {
        if (show) {
            $('#loading-indicator').show();
            $('#empty-state').hide();
            $('#data-display').hide();
        } else {
            $('#loading-indicator').hide();
        }
    }

    showError(message) {
        $('#error-message').text(message);
        $('#error-alert').show();
        setTimeout(() => {
            $('#error-alert').fadeOut();
        }, 5000);
    }

    hideError() {
        $('#error-alert').hide();
    }

    showTestImage() {
        if (!this.currentData) {
            this.showError('请先查询数据');
            return;
        }

        // 显示图片信息
        const imageInfo = $('#image-info');
        imageInfo.html(`
            <div class="row">
                <div class="col-md-6">
                    <strong>零件:</strong> ${this.currentData.part_name || '-'}<br>
                    <strong>材料:</strong> ${this.currentData.material_name || '-'}<br>
                    <strong>克重:</strong> ${this.currentData.weight ? this.currentData.weight + 'g/m²' : '-'}
                </div>
                <div class="col-md-6">
                    <strong>测试日期:</strong> ${this.currentData.test_date || '-'}<br>
                    <strong>测试机构:</strong> ${this.currentData.test_institution || '-'}<br>
                    <strong>测试工程师:</strong> ${this.currentData.test_engineer || '-'}
                </div>
            </div>
        `);

        // 显示图片
        const testImage = $('#test-image');
        const noImage = $('#no-image');

        if (this.currentData.test_image_path) {
            testImage.attr('src', this.currentData.test_image_path).show();
            noImage.hide();
        } else {
            testImage.hide();
            noImage.show();
        }

        // 显示模态框
        $('#imageModal').modal('show');
    }
}


