/**
 * 标签页管理器
 * 负责标签页的创建、切换、关闭和内容加载
 */
class TabManager {
    constructor() {
        this.tabs = new Map(); // 存储已打开的标签页
        this.activeTabId = 'home-tab-pane'; // 当前活跃的标签页ID
        this.tabCounter = 0; // 标签页计数器
        this.tabNavigation = document.getElementById('tabNavigation');
        this.mainTabs = document.getElementById('mainTabs');
        this.tabContent = document.getElementById('tabContent');
        
        // 标签页配置
        this.tabConfig = {
            '/modal/search': {
                title: '模态数据查询',
                icon: 'fas fa-wave-square'
            },
            '/airtightness/comparison': {
                title: '气密性泄漏量对比',
                icon: 'fas fa-tachometer-alt'
            },
            '/airtightness/images': {
                title: '气密性测试图片',
                icon: 'fas fa-images'
            },
            '/sound_insulation/area_comparison': {
                title: '区域隔声量对比',
                icon: 'fas fa-volume-up'
            },
            '/sound_insulation/vehicle_insulation': {
                title: '车型隔声量对比',
                icon: 'fas fa-car'
            },
            '/sound_insulation/vehicle_reverberation': {
                title: '车型混响时间对比',
                icon: 'fas fa-wave-square'
            },
            '/sound_absorption/coefficient_query': {
                title: '垂直入射吸音系数查询',
                icon: 'fas fa-search'
            },
            '/sound_transmission/transmission_loss_query': {
                title: '垂直入射法隔声量查询',
                icon: 'fas fa-shield-alt'
            },
            '/wall_mounted_transmission/transmission_loss_query': {
                title: '上墙法隔声量查询',
                icon: 'fas fa-wall-brick'
            },
            '/material_porosity_flow_resistance/query': {
                title: '孔隙率流阻查询',
                icon: 'fas fa-filter'
            }
        };
        
        this.init();
    }
    
    /**
     * 初始化标签页管理器
     */
    init() {
        // 绑定事件
        this.bindEvents();
        console.log('TabManager initialized');
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 监听标签页点击事件
        this.mainTabs.addEventListener('click', (e) => {
            if (e.target.classList.contains('nav-link')) {
                e.preventDefault();
                const tabId = e.target.getAttribute('data-bs-target').substring(1);
                this.switchTab(tabId);
            }
        });
        
        // 监听标签页关闭事件
        this.mainTabs.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-close') || e.target.closest('.btn-close')) {
                e.preventDefault();
                e.stopPropagation();
                const closeBtn = e.target.classList.contains('btn-close') ? e.target : e.target.closest('.btn-close');
                const tabId = closeBtn.getAttribute('data-tab-id');
                this.closeTab(tabId);
            }
        });
    }
    
    /**
     * 打开新标签页
     * @param {string} url - 页面URL
     * @param {string} title - 标签页标题（可选）
     * @param {string} icon - 图标类名（可选）
     */
    async openTab(url, title = null, icon = null) {
        // 检查是否已经打开了该标签页
        const existingTab = Array.from(this.tabs.values()).find(tab => tab.url === url);
        if (existingTab) {
            this.switchTab(existingTab.id);
            return;
        }
        
        // 获取标签页配置
        const config = this.tabConfig[url] || {};
        title = title || config.title || '未知页面';
        icon = icon || config.icon || 'fas fa-file';
        
        // 生成标签页ID
        this.tabCounter++;
        const tabId = `tab-${this.tabCounter}`;
        
        // 创建标签页数据
        const tabData = {
            id: tabId,
            title: title,
            icon: icon,
            url: url,
            active: false,
            loaded: false,
            content: null
        };
        
        // 添加到标签页集合
        this.tabs.set(tabId, tabData);
        
        // 创建标签页UI
        this.createTabUI(tabData);
        
        // 显示标签页导航栏
        this.showTabNavigation();
        
        // 加载内容并切换到新标签页
        await this.loadContent(url, tabId);
        this.switchTab(tabId);
    }
    
    /**
     * 创建标签页UI
     * @param {Object} tabData - 标签页数据
     */
    createTabUI(tabData) {
        // 创建标签页导航项
        const tabNav = document.createElement('li');
        tabNav.className = 'nav-item';
        tabNav.innerHTML = `
            <a class="nav-link d-flex align-items-center" 
               id="${tabData.id}-tab" 
               data-bs-toggle="tab" 
               data-bs-target="#${tabData.id}-pane" 
               type="button" 
               role="tab">
                <i class="${tabData.icon} me-2"></i>
                <span class="tab-title">${tabData.title}</span>
                <button type="button" class="btn-close ms-2" data-tab-id="${tabData.id}" title="关闭标签页"></button>
            </a>
        `;
        
        // 创建标签页内容容器
        const tabPane = document.createElement('div');
        tabPane.className = 'tab-pane fade';
        tabPane.id = `${tabData.id}-pane`;
        tabPane.setAttribute('role', 'tabpanel');
        tabPane.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        `;
        
        // 添加到DOM
        this.mainTabs.appendChild(tabNav);
        this.tabContent.appendChild(tabPane);
    }
    
    /**
     * 切换标签页
     * @param {string} tabId - 标签页ID
     */
    switchTab(tabId) {
        // 更新活跃标签页
        this.activeTabId = tabId;
        
        // 更新UI状态
        this.updateTabStates();
        
        // 如果是首页，隐藏标签页导航栏
        if (tabId === 'home-tab-pane') {
            this.hideTabNavigation();
        } else {
            this.showTabNavigation();
        }
    }
    
    /**
     * 关闭标签页
     * @param {string} tabId - 标签页ID
     */
    closeTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;
        
        // 移除DOM元素
        const tabNav = document.getElementById(`${tabId}-tab`).closest('.nav-item');
        const tabPane = document.getElementById(`${tabId}-pane`);
        
        if (tabNav) tabNav.remove();
        if (tabPane) tabPane.remove();
        
        // 从标签页集合中移除
        this.tabs.delete(tabId);
        
        // 如果关闭的是当前活跃标签页，需要切换到其他标签页
        if (this.activeTabId === tabId) {
            if (this.tabs.size > 0) {
                // 切换到最后一个标签页
                const lastTab = Array.from(this.tabs.keys()).pop();
                this.switchTab(lastTab);
            } else {
                // 没有其他标签页，切换到首页
                this.switchTab('home-tab-pane');
            }
        }
        
        // 如果没有标签页了，隐藏导航栏
        if (this.tabs.size === 0) {
            this.hideTabNavigation();
        }
    }
    
    /**
     * 加载页面内容
     * @param {string} url - 页面URL
     * @param {string} tabId - 标签页ID
     */
    async loadContent(url, tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab || tab.loaded) return;
        
        const tabPane = document.getElementById(`${tabId}-pane`);
        if (!tabPane) return;
        
        try {
            // 发送AJAX请求
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'text/html'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const html = await response.text();

            // 解析HTML内容，提取content部分
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const contentBlock = doc.querySelector('[data-content-block]');

            let content;
            if (contentBlock) {
                // AJAX模式，直接使用data-content-block内容
                content = contentBlock.innerHTML;
            } else {
                // 完整页面模式，提取body内容
                content = doc.body.innerHTML;
            }

            // 更新标签页内容
            tabPane.innerHTML = content;
            
            // 标记为已加载
            tab.loaded = true;
            tab.content = contentBlock.innerHTML;
            
            // 重新初始化页面JavaScript
            this.initializePageScripts(tabPane, url);
            
        } catch (error) {
            console.error('Failed to load tab content:', error);
            tabPane.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    页面加载失败: ${error.message}
                    <button type="button" class="btn btn-outline-danger btn-sm ms-3" onclick="tabManager.reloadTab('${tabId}')">
                        <i class="fas fa-redo me-1"></i>重新加载
                    </button>
                </div>
            `;
        }
    }
    
    /**
     * 重新加载标签页
     * @param {string} tabId - 标签页ID
     */
    async reloadTab(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;
        
        tab.loaded = false;
        await this.loadContent(tab.url, tabId);
    }
    
    /**
     * 初始化页面脚本
     * @param {Element} container - 容器元素
     * @param {string} url - 页面URL
     */
    initializePageScripts(container, url) {
        // 执行容器内的script标签
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.src) {
                // 外部脚本，动态加载
                const newScript = document.createElement('script');
                newScript.src = script.src;
                newScript.async = false;
                document.head.appendChild(newScript);
            } else if (script.textContent) {
                // 内联脚本，直接执行
                try {
                    eval(script.textContent);
                } catch (error) {
                    console.error('Error executing inline script:', error);
                }
            }
        });

        // 加载CSS文件
        const links = container.querySelectorAll('link[rel="stylesheet"]');
        links.forEach(link => {
            if (!document.querySelector(`link[href="${link.href}"]`)) {
                const newLink = document.createElement('link');
                newLink.rel = 'stylesheet';
                newLink.href = link.href;
                document.head.appendChild(newLink);
            }
        });

        // 触发自定义事件，让各个页面的JavaScript可以监听并重新初始化
        const event = new CustomEvent('tabContentLoaded', {
            detail: { container, url }
        });
        document.dispatchEvent(event);

        // 延迟触发，确保DOM完全加载
        setTimeout(() => {
            const readyEvent = new CustomEvent('tabContentReady', {
                detail: { container, url }
            });
            document.dispatchEvent(readyEvent);
        }, 100);
    }
    
    /**
     * 更新标签页状态
     */
    updateTabStates() {
        // 更新导航标签状态
        document.querySelectorAll('#mainTabs .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        document.querySelectorAll('#tabContent .tab-pane').forEach(pane => {
            pane.classList.remove('show', 'active');
        });
        
        // 激活当前标签页
        const activeTab = document.getElementById(`${this.activeTabId}-tab`) || 
                         document.querySelector(`[data-bs-target="#${this.activeTabId}"]`);
        const activePane = document.getElementById(this.activeTabId);
        
        if (activeTab) activeTab.classList.add('active');
        if (activePane) activePane.classList.add('show', 'active');
    }
    
    /**
     * 显示标签页导航栏
     */
    showTabNavigation() {
        this.tabNavigation.style.display = 'block';
    }
    
    /**
     * 隐藏标签页导航栏
     */
    hideTabNavigation() {
        this.tabNavigation.style.display = 'none';
    }
    
    /**
     * 获取所有打开的标签页
     */
    getAllTabs() {
        return Array.from(this.tabs.values());
    }
    
    /**
     * 获取当前活跃的标签页
     */
    getActiveTab() {
        return this.tabs.get(this.activeTabId);
    }
}

// 全局实例
let tabManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    tabManager = new TabManager();
});
