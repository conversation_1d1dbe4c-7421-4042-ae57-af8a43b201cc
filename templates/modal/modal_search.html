{% if not ajax_mode %}
{% extends "base.html" %}

{% block title %}模态数据查询 - NVH数据管理系统{% endblock %}

{% block head %}
<link href="{{ url_for('static', filename='css/modal_search.css') }}" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/modal_search.js') }}"></script>
{% endblock %}

{% block content %}
{% endif %}
<div data-content-block>
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">模态数据查询</h1>
</div>

<!-- 搜索区域 -->
<div class="card mb-4">
    <div class="card-header p-0">
        <div class="search-tabs">
            <button class="tab-btn active" data-tab="modal-search" id="modal-search-tab">
                <i class="fas fa-search me-2"></i>模态数据搜索
            </button>
            <button class="tab-btn" data-tab="comparison-search" id="comparison-search-tab">
                <i class="fas fa-chart-line me-2"></i>对标搜索
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="tab-content" id="search-tab-content">
            <!-- 模态数据搜索面板 -->
            <div class="tab-pane fade show active" id="modal-search-pane" role="tabpanel">
                <form id="search-form" class="row g-3 align-items-center">
                    <div class="col-md-4">
                        <div class="row align-items-center g-1">
                            <div class="col-auto">
                                <label for="vehicle-select" class="form-label mb-0 me-2">车型选择:</label>
                            </div>
                            <div class="col">
                                <select class="form-select" id="vehicle-select" required>
                                    <option value="">请选择车型</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="row align-items-center g-1">
                            <div class="col-auto">
                                <label for="component-select" class="form-label mb-0 me-2">零件选择:</label>
                            </div>
                            <div class="col">
                                <div class="custom-multiselect" id="component-multiselect">
                                    <div class="multiselect-trigger">
                                        <span class="multiselect-placeholder">全部零件</span>
                                        <span class="multiselect-count">已选: 0</span>
                                        <i class="multiselect-arrow fas fa-chevron-down"></i>
                                    </div>
                                    <div class="multiselect-dropdown">
                                        <div class="multiselect-loading">请先选择车型</div>
                                    </div>
                                </div>
                                <!-- 隐藏的多选框，用于保存选择的值 -->
                                <select class="form-select d-none" id="component-select" multiple>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex gap-2">
                            <button type="button" id="search-btn" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>搜索
                            </button>
                            <button type="button" id="export-btn" class="btn btn-outline-success" disabled>
                                <i class="fas fa-download me-1"></i>导出数据
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 对标搜索面板 -->
            <div class="tab-pane fade" id="comparison-search-pane" role="tabpanel">
                <form id="comparison-form" class="row g-3 align-items-start">
                    <div class="col-md-3">
                        <div class="row align-items-center mb-1">
                            <div class="col-3">
                                <label for="comparison-components" class="form-label mb-0 text-end">零件选择:</label>
                            </div>
                            <div class="col-9">
                                <select class="form-select" id="comparison-components">
                                    <option value="">请选择零件</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="row align-items-start mb-1">
                            <div class="col-3">
                                <label for="comparison-vehicles" class="form-label mb-0 text-end">车型选择:</label>
                            </div>
                            <div class="col-9">
                                <div class="custom-multiselect" id="comparison-vehicles-multiselect">
                                    <div class="multiselect-trigger">
                                        <span class="multiselect-placeholder">请选择车型</span>
                                        <span class="multiselect-count">已选: 0</span>
                                        <i class="multiselect-arrow fas fa-chevron-down"></i>
                                    </div>
                                    <div class="multiselect-dropdown">
                                        <div class="vehicle-search-container" style="padding: 8px; border-bottom: 1px solid #e9ecef;">
                                            <input type="text" class="form-control form-control-sm" id="vehicle-search" placeholder="搜索车型...">
                                        </div>
                                        <div id="vehicle-options-container">
                                            <div class="multiselect-loading">加载中...</div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 隐藏的多选框，用于保存选择的值 -->
                                <select class="form-select d-none" id="comparison-vehicles" multiple>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="row align-items-start mb-1">
                            <div class="col-4">
                                <label for="comparison-conditions" class="form-label mb-0 text-end">测试状态:</label>
                            </div>
                            <div class="col-8">
                                <div class="custom-multiselect" id="comparison-conditions-multiselect">
                                    <div class="multiselect-trigger">
                                        <span class="multiselect-placeholder">请选择测试状态</span>
                                        <span class="multiselect-count">已选: 0</span>
                                        <i class="multiselect-arrow fas fa-chevron-down"></i>
                                    </div>
                                    <div class="multiselect-dropdown">
                                        <div class="multiselect-loading">请先选择零件</div>
                                    </div>
                                </div>
                                <!-- 隐藏的原生select，用于表单提交 -->
                                <select class="form-select d-none" id="comparison-conditions" multiple>
                                    <option value="">请选择测试状态</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="row align-items-start mb-1">
                            <div class="col-4">
                                <label for="comparison-modes" class="form-label mb-0 text-end">模态振型:</label>
                            </div>
                            <div class="col-8">
                                <div class="custom-multiselect" id="comparison-modes-multiselect">
                                    <div class="multiselect-trigger">
                                        <span class="multiselect-placeholder">请选择模态振型</span>
                                        <span class="multiselect-count">已选: 0</span>
                                        <i class="multiselect-arrow fas fa-chevron-down"></i>
                                    </div>
                                    <div class="multiselect-dropdown">
                                        <div class="multiselect-loading">请先选择零件</div>
                                    </div>
                                </div>
                                <!-- 隐藏的原生select，用于表单提交 -->
                                <select class="form-select d-none" id="comparison-modes" multiple>
                                    <option value="">请选择模态振型</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex flex-column gap-2 align-items-center">
                            <button type="button" id="comparison-btn" class="btn btn-success btn-sm px-3">
                                <i class="fas fa-chart-bar me-1"></i>生成对标
                            </button>
                            <button type="button" id="comparison-export-btn" class="btn btn-outline-info btn-sm px-3" disabled>
                                <i class="fas fa-download me-1"></i>导出对标
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0" id="result-title">
            <i class="fas fa-table me-2"></i>搜索结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span id="result-count" class="badge bg-secondary">0 条记录</span>
            <button type="button" id="current-export-btn" class="btn btn-outline-success btn-sm" disabled>
                <i class="fas fa-download me-1"></i>导出当前结果
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- 模态数据搜索结果表格 -->
        <div id="modal-search-results" class="result-container">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>分类</th>
                            <th>子分类</th>
                            <th>零件名称</th>
                            <th>频率 (Hz)</th>
                            <th>模态类型</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="table-body">
                        <tr>
                            <td colspan="7" class="text-center text-muted py-4">
                                <i class="fas fa-search fa-2x mb-2"></i><br>
                                请选择搜索条件并点击搜索按钮
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 对标结果 -->
        <div id="comparison-results" class="result-container" style="display: none;">
            <!-- 对标表格 -->
            <div id="comparison-table-container" class="mb-4" style="display: none;">
                <h6>模态频率对比表</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm" id="comparison-table">
                        <thead class="table-light">
                            <!-- 动态生成表头 -->
                        </thead>
                        <tbody>
                            <!-- 动态生成表格内容 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 对标图表 -->
            <div id="comparison-chart-container" style="display: none;">
                <h6>模态频率对比图</h6>
                <div class="chart-wrapper">
                    <canvas id="comparison-chart"></canvas>
                </div>
            </div>

            <!-- 空状态提示 -->
            <div id="comparison-empty" class="text-center text-muted py-4">
                <i class="fas fa-chart-line fa-2x mb-2"></i><br>
                请选择对标条件并点击生成对标按钮
            </div>
        </div>
    </div>
</div>

<!-- 模态振型查看弹窗 -->
<div class="modal fade" id="modal-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模态振型查看</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modal-detail-content">
                    <!-- 详情内容将通过JS动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
</div>
{% if not ajax_mode %}
{% endblock %}
{% endif %}

{% if ajax_mode %}
<!-- AJAX模式下需要的CSS和JS -->
<link href="{{ url_for('static', filename='css/modal_search.css') }}" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/modal_search.js') }}"></script>
{% endif %}


