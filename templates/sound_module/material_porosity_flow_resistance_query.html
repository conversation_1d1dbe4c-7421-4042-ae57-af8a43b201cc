{% if ajax_mode %}
<!-- AJAX模式：只渲染内容部分 -->
{% else %}
<!-- 完整页面模式 -->
{% extends "base.html" %}

{% block title %}材料孔隙率流阻查询 - NVH数据管理系统{% endblock %}

{% block head %}
<link href="{{ url_for('static', filename='css/material_porosity_flow_resistance_query.css') }}" rel="stylesheet">
<script src="{{ url_for('static', filename='js/material_porosity_flow_resistance.js') }}"></script>
{% endblock %}

{% block content %}
{% endif %}
<div data-content-block>
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">材料孔隙率流阻查询</h1>
</div>

<!-- 查询条件卡片 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row g-3 align-items-start">
                <!-- 零件选择 -->
                <div class="col-md-4">
                    <select class="form-select" id="part-select">
                        <option value="">加载中...</option>
                    </select>
                </div>

                <!-- 材料选择 -->
                <div class="col-md-4">
                    <div class="material-multiselect" id="material-multiselect">
                        <div class="multiselect-container">
                            <div class="multiselect-input-container">
                                <input type="text" class="form-control multiselect-input" placeholder="请先选择零件..." readonly>
                                <i class="fas fa-chevron-down multiselect-arrow"></i>
                            </div>
                            <div class="multiselect-dropdown">
                                <div class="multiselect-search">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索材料...">
                                </div>
                                <div class="multiselect-options">
                                    <!-- 动态加载选项 -->
                                </div>
                            </div>
                        </div>
                        <div class="selected-items mt-2">
                            <!-- 已选择的材料标签 -->
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="col-md-4 d-flex align-items-start">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" id="query-btn" disabled>
                            <i class="fas fa-search me-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="reset-btn">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="button" class="btn btn-outline-success" id="export-btn" disabled>
                            <i class="fas fa-download me-1"></i>导出
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>孔隙率流阻查询结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span class="badge bg-info" id="result-count">0 条记录</span>
        </div>
    </div>
    <div class="card-body">
        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="data-table">
                <thead class="table-dark">
                    <tr>
                        <th>零件名称</th>
                        <th>材料组成</th>
                        <th>厚度<br>(mm)</th>
                        <th>克重<br>(g/m²)</th>
                        <th>密度<br>(kg/m³)</th>
                        <th>孔隙率<br>(%)</th>
                        <th>孔隙率偏差<br>(%)</th>
                        <th>流阻率<br>(Pa·s/m²)</th>
                        <th>流阻率偏差<br>(Pa·s/m²)</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择查询条件</h5>
        <p class="text-muted">选择零件和材料，点击"查询"按钮查看孔隙率流阻数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在查询数据...</p>
</div>

<!-- 错误提示 -->
<div class="alert alert-danger" id="error-alert" style="display: none;">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <span id="error-message"></span>
</div>
</div>

{% if ajax_mode %}
<!-- AJAX模式下需要的CSS和JS -->
<link href="{{ url_for('static', filename='css/material_porosity_flow_resistance_query.css') }}" rel="stylesheet">
<script src="{{ url_for('static', filename='js/material_porosity_flow_resistance.js') }}"></script>
<script>
    // 初始化材料孔隙率流阻查询功能
    $(document).ready(function() {
        new MaterialPorosityFlowResistanceQuery();
    });
</script>
{% else %}
<!-- 完整页面模式结束 -->
{% endblock %}

{% block extra_js %}
<script>
    // 初始化材料孔隙率流阻查询功能
    $(document).ready(function() {
        new MaterialPorosityFlowResistanceQuery();
    });
</script>
{% endblock %}
{% endif %}
