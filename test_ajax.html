<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX测试</title>
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ccc; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>AJAX请求测试</h1>
    
    <div class="test-section">
        <h3>测试模态数据查询页面</h3>
        <button onclick="testAjaxRequest('/modal/search')">测试 /modal/search</button>
        <div id="modal-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>测试气密性对比页面</h3>
        <button onclick="testAjaxRequest('/airtightness/comparison')">测试 /airtightness/comparison</button>
        <div id="airtightness-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>测试吸音系数查询页面</h3>
        <button onclick="testAjaxRequest('/sound_absorption/coefficient_query')">测试 /sound_absorption/coefficient_query</button>
        <div id="absorption-result" class="result" style="display: none;"></div>
    </div>
    
    <script>
        async function testAjaxRequest(url) {
            const resultId = url.split('/')[1] + '-result';
            const resultDiv = document.getElementById(resultId);
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'text/html'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const html = await response.text();
                
                // 检查是否包含data-content-block
                const hasContentBlock = html.includes('data-content-block');
                const hasBaseHtml = html.includes('<!DOCTYPE html>');
                
                let message = `✅ 请求成功 (${response.status})\n`;
                message += `📄 响应长度: ${html.length} 字符\n`;
                message += `🏷️ 包含 data-content-block: ${hasContentBlock ? '是' : '否'}\n`;
                message += `📋 完整HTML页面: ${hasBaseHtml ? '是' : '否'}\n`;
                
                if (hasContentBlock && !hasBaseHtml) {
                    message += `✨ AJAX模式正常工作！`;
                    resultDiv.className = 'result success';
                } else if (hasBaseHtml) {
                    message += `⚠️ 返回了完整页面，可能AJAX检测失败`;
                    resultDiv.className = 'result error';
                } else {
                    message += `❓ 响应格式异常`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.innerHTML = `<pre>${message}</pre>`;
                
            } catch (error) {
                resultDiv.innerHTML = `<pre>❌ 请求失败: ${error.message}</pre>`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
