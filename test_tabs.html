<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签页功能测试</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 标签页样式 -->
    <link href="static/css/tab-manager.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .test-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .test-links a {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">标签页功能测试</h1>
        
        <!-- 标签页导航栏 -->
        <div class="tab-navigation" id="tabNavigation" style="display: none;">
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <!-- 动态生成的标签页 -->
            </ul>
        </div>

        <!-- 标签页内容区域 -->
        <div class="tab-content" id="tabContent">
            <!-- 默认首页内容 -->
            <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel">
                <div class="test-section">
                    <h3>测试链接</h3>
                    <p>点击下面的链接测试标签页功能：</p>
                    <div class="test-links">
                        <a href="/modal/search" class="btn btn-primary">
                            <i class="fas fa-wave-square me-2"></i>模态数据查询
                        </a>
                        <a href="/airtightness/comparison" class="btn btn-info">
                            <i class="fas fa-tachometer-alt me-2"></i>气密性泄漏量对比
                        </a>
                        <a href="/airtightness/images" class="btn btn-secondary">
                            <i class="fas fa-images me-2"></i>气密性测试图片
                        </a>
                        <a href="/sound_insulation/area_comparison" class="btn btn-success">
                            <i class="fas fa-volume-up me-2"></i>区域隔声量对比
                        </a>
                        <a href="/sound_absorption/coefficient_query" class="btn btn-warning">
                            <i class="fas fa-search me-2"></i>垂直入射吸音系数查询
                        </a>
                        <a href="/sound_transmission/transmission_loss_query" class="btn btn-danger">
                            <i class="fas fa-shield-alt me-2"></i>垂直入射法隔声量查询
                        </a>
                        <a href="/wall_mounted_transmission/transmission_loss_query" class="btn btn-dark">
                            <i class="fas fa-wall-brick me-2"></i>上墙法隔声量查询
                        </a>
                        <a href="/material_porosity_flow_resistance/query" class="btn btn-outline-primary">
                            <i class="fas fa-filter me-2"></i>孔隙率流阻查询
                        </a>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>功能说明</h3>
                    <ul>
                        <li>点击上面的链接会在新标签页中打开对应的页面</li>
                        <li>可以同时打开多个标签页</li>
                        <li>点击标签页可以切换显示的内容</li>
                        <li>点击标签页右侧的 × 按钮可以关闭标签页</li>
                        <li>关闭所有标签页后会回到首页</li>
                    </ul>
                </div>
                
                <div class="test-section">
                    <h3>调试信息</h3>
                    <div id="debug-info">
                        <p>TabManager状态：<span id="tab-manager-status">未初始化</span></p>
                        <p>当前活跃标签页：<span id="active-tab">home-tab-pane</span></p>
                        <p>已打开标签页数量：<span id="tab-count">0</span></p>
                        <button class="btn btn-sm btn-outline-secondary" onclick="updateDebugInfo()">刷新调试信息</button>
                    </div>
                </div>
            </div>
            <!-- 动态加载的标签页内容 -->
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 标签页管理器 -->
    <script src="static/js/tab-manager.js"></script>
    
    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 等待TabManager初始化
            const waitForTabManager = () => {
                if (typeof tabManager !== 'undefined' && tabManager !== null) {
                    console.log('TabManager已初始化');
                    document.getElementById('tab-manager-status').textContent = '已初始化';
                    bindTestLinks();
                    updateDebugInfo();
                } else {
                    setTimeout(waitForTabManager, 100);
                }
            };
            waitForTabManager();
        });
        
        // 绑定测试链接
        function bindTestLinks() {
            const testLinks = document.querySelectorAll('.test-links a[href]');
            testLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const url = this.getAttribute('href');
                    const iconElement = this.querySelector('i');
                    const icon = iconElement ? iconElement.className : 'fas fa-file';
                    const title = this.textContent.trim();
                    
                    console.log('打开标签页:', { url, title, icon });
                    
                    if (tabManager) {
                        tabManager.openTab(url, title, icon);
                        setTimeout(updateDebugInfo, 500);
                    }
                });
            });
        }
        
        // 更新调试信息
        function updateDebugInfo() {
            if (typeof tabManager !== 'undefined' && tabManager !== null) {
                document.getElementById('tab-manager-status').textContent = '已初始化';
                document.getElementById('active-tab').textContent = tabManager.activeTabId;
                document.getElementById('tab-count').textContent = tabManager.tabs.size;
            } else {
                document.getElementById('tab-manager-status').textContent = '未初始化';
                document.getElementById('active-tab').textContent = '未知';
                document.getElementById('tab-count').textContent = '0';
            }
        }
        
        // 监听标签页事件
        document.addEventListener('tabContentLoaded', function(e) {
            console.log('标签页内容已加载:', e.detail);
            updateDebugInfo();
        });
        
        document.addEventListener('tabContentReady', function(e) {
            console.log('标签页内容已就绪:', e.detail);
        });
    </script>
</body>
</html>
